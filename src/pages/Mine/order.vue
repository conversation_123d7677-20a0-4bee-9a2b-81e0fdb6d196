<template>
    <div class="box">
        <h4 class="box_title">我的订单</h4>
        <div class="box_tabs">
            <div :class="['box_tabs_item', { active: item.id === currentId }]" v-for="item in tablsit" :key="item.id"
                @click="changeTab(item.id)">
                {{ item.name }}
            </div>
        </div>
        <div class="box_list" v-loading="loading">
            <div class="box_list_item" v-for="order in orderList" :key="order.number">
                <div class="box_list_item_time">
                    {{ formatDate(order.created_at) }} {{ t('center.orderNo') }}：{{ order.number }}
                </div>
                <div class="box_list_item_main">
                    <div class="box_list_item_main_td w383">
                        <img :src="order.showUrl || 'https://p0.ssl.img.360kuai.com/dmfd/158_88_75/t11508c75c8423c35fb6c06b871.webp?size=1024x819'"
                            class="box_list_item_main_td_img" />
                        <div class="detail">
                            <div class="detail_name">{{ order.product_name || 'Ledger Flex' }}</div>
                            <div class="detail_num">x {{ order.quantity || 1 }}</div>
                            <!-- <div class="detail_more">
                                共 {{ order.total || 1 }} 件商品
                            </div> -->
                        </div>
                    </div>
                    <div class="box_list_item_main_td w122">
                        {{ order.total_format }}
                    </div>
                    <div class="box_list_item_main_td w200">
                        <span>{{ order.status_format || getOrderStatusText(order.status) }}</span>
                    </div>
                    <div class="box_list_item_main_td flex flex1">
                        <div v-if="order.status === 'unpaid'" class="pay" @click="payOrder(order)">去支付</div>
                        <div v-if="order.status === 'unpaid'" class="info" @click="cancelOrder(order)">取消订单</div>
                        <div v-if="order.status === 'shipped'" class="pay" @click="confirmOrder(order)">确认收货</div>
                        <!-- <div class="info" @click="viewOrderDetail(order)">查看详情</div> -->
                    </div>
                </div>
            </div>
            <div v-if="orderList.length === 0 && !loading" class="empty_state">
                <p>暂无订单数据</p>
            </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > pagination.limit" class="pagination">
            <button :disabled="pagination.page <= 1" @click="changePage(pagination.page - 1)" class="page_btn">
                上一页
            </button>
            <span class="page_info">
                {{ pagination.page }} / {{ Math.ceil(pagination.total / pagination.limit) }}
            </span>
            <button :disabled="pagination.page >= Math.ceil(pagination.total / pagination.limit)"
                @click="changePage(pagination.page + 1)" class="page_btn">
                下一页
            </button>
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { GetOrderList, CancelOrder as CancelOrderAPI, CompleteOrder } from '@api/order';
import { ElMessage, ElMessageBox } from 'element-plus';

const router = useRouter();
const { t, locale } = useI18n();

const activeName = ref('0');
const loading = ref(false);
const orderList = ref([]);
const pagination = ref({
    page: 1,
    limit: 10,
    total: 0
});

const tablsit = ref([
    {
        id: '0',
        name: '所有订单',
        status: null
    },
    {
        id: '1',
        name: '待支付',
        status: 'unpaid'
    },
    {
        id: '2',
        name: '待发货',
        status: 'paid'
    },
    {
        id: '3',
        name: '待收货',
        status: 'shipped'
    },
    {
        id: '4',
        name: '已完成',
        status: 'completed'
    }
])
const currentId = ref('0');

// 获取订单列表
const loadOrderList = async () => {
    try {
        loading.value = true;

        const currentTab = tablsit.value.find(item => item.id === currentId.value);
        const params = {
            page: pagination.value.page,
            limit: pagination.value.limit
        };

        if (currentTab && currentTab.status) {
            params.status = currentTab.status;
        }

        const response = await GetOrderList(params);
        console.log('response123123', response);

        orderList.value = response.orders.map(order => ({
            ...order,
            total_format: `$${order.total}`,
            showUrl: `http://************/${order.image}`
        })) || [];
        pagination.value.total = response.total || 0;

    } catch (error) {
        console.error('获取订单列表失败:', error);
        ElMessage.error('获取订单列表失败');
    } finally {
        loading.value = false;
    }
};

// 切换标签
const changeTab = (tabId) => {
    currentId.value = tabId;
    pagination.value.page = 1; // 重置到第一页
    loadOrderList();
};

// 分页切换
const changePage = (page) => {
    pagination.value.page = page;
    loadOrderList();
};

// 格式化日期
const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
    const statusMap = {
        'pending_payment': '待支付',
        'processing': '待发货',
        'shipped': '待收货',
        'completed': '已完成',
        'cancelled': '已取消',
        'refund_pending': '退款中'
    };
    return statusMap[status] || status;
};

// 支付订单
const payOrder = (order) => {
    router.push(`/pay?order_number=${order.number}`);
};

// 取消订单
const cancelOrder = async (order) => {
    try {
        await ElMessageBox.confirm('确定要取消这个订单吗？', '确认取消', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        await CancelOrderAPI(order.number);
        ElMessage.success('订单已取消');
        loadOrderList(); // 重新加载列表
    } catch (error) {
        if (error !== 'cancel') {
            console.error('取消订单失败:', error);
            ElMessage.error('取消订单失败');
        }
    }
};

// 确认收货
const confirmOrder = async (order) => {
    try {
        await ElMessageBox.confirm('确定已收到货物吗？', '确认收货', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        });

        await CompleteOrder(order.number);
        ElMessage.success('订单已完成');
        loadOrderList(); // 重新加载列表
    } catch (error) {
        if (error !== 'cancel') {
            console.error('确认收货失败:', error);
            ElMessage.error('确认收货失败');
        }
    }
};

// 查看订单详情
const viewOrderDetail = (order) => {
    router.push(`/mine/orderDetail?order_number=${order.number}`);
};

const handleClick = (tab, event) => {
    console.log(tab, event)
}

onMounted(() => {
    loadOrderList();
});

</script>
<style lang="scss" scoped>
@import url('./order.scss');

.detail_more {
    font-size: 1.2rem;
    color: #999;
    margin-top: 0.5rem;
}

.info {
    cursor: pointer;
    margin-left: 0.5rem;
    color: #333
}

.pay {
    cursor: pointer;
    margin-left: 0.5rem;
    color: #fff
}

.empty_state {
    text-align: center;
    padding: 3rem;
    color: #999;
    font-size: 1.4rem;
    padding-top: 20rem;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    gap: 1rem;

    .page_btn {
        padding: 0.8rem 1.6rem;
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 0.4rem;
        cursor: pointer;

        &:hover:not(:disabled) {
            background: #f5f5f5;
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }

    .page_info {
        font-size: 1.4rem;
        color: #666;
    }
}

.box_tabs_item {
    cursor: pointer;
}
</style>