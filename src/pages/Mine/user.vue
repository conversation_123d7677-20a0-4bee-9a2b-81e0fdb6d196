<template>
    <div class="user_box">
        <h4 class="user_box_title">个人信息</h4>
        <h4 class="user_box_line"></h4>
        <div class="user_box_main" v-loading="loading">
            <img :src="userForm.avatar || '@assets/avator.png'" class="user_box_main_avator" />
            <div class="user_box_main_form">
                <el-upload v-model:file-list="fileList" class="upload-demo" :http-request="customUpload"
                    :show-file-list="false" :before-upload="beforeAvatarUpload" :disabled="uploadLoading">
                    <div class="user_box_main_form_upload">
                        {{ uploadLoading ? '上传中...' : t('center.upload') }}
                        <img src="@assets/upload.png" class="user_box_main_form_upload_icon" />
                    </div>
                </el-upload>
                <p class="user_box_main_form_tip">{{ t('center.tip') }}</p>
                <div class="user_box_main_form_control">
                    <input v-model="userForm.name" class="user_box_main_form_control_val" placeholder="请输入用户名" />
                </div>
                <div class="user_box_main_form_control">
                    <input v-model="userForm.email" class="user_box_main_form_control_val" placeholder="请输入邮箱"
                        type="email" />
                </div>
                <div class="user_box_main_form_btn" @click="onSubmit" :disabled="submitLoading">
                    {{ submitLoading ? '提交中...' : t('center.submit') }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted, inject } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { GetAccountInfo, UpdateUserInfo, UploadFile } from '@api/account';

const { t, locale } = useI18n();
const router = useRouter();

// 注入父组件提供的刷新用户信息方法
const refreshUserInfo = inject('refreshUserInfo', () => { });

const imageUrl = ref('');
const loading = ref(false);
const submitLoading = ref(false);
const uploadLoading = ref(false);
const fileList = ref([]);

const userForm = reactive({
    name: '',
    email: '',
    avatar: ''
});

// 获取用户信息
const loadUserInfo = async () => {
    try {
        loading.value = true;
        const response = await GetAccountInfo();
        console.log('用户信息:', response);

        if (response) {
            userForm.name = response.customer.name || '';
            userForm.email = response.customer.email || '';
            userForm.avatar = response.customer.avatar || '';
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
        if (error.message?.includes('401')) {
            ElMessage.error('请先登录');
            router.push('/login');
        } else {
            ElMessage.error('获取用户信息失败');
        }
    } finally {
        loading.value = false;
    }
};

// 提交更新
const onSubmit = async () => {
    // 表单验证
    if (!userForm.name.trim()) {
        ElMessage.error('请输入用户名');
        return;
    }

    if (!userForm.email.trim()) {
        ElMessage.error('请输入邮箱');
        return;
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userForm.email)) {
        ElMessage.error('请输入正确的邮箱格式');
        return;
    }

    try {
        submitLoading.value = true;

        const updateData = {
            name: userForm.name,
            email: userForm.email,
            avatar: userForm.avatar,
            data: {}
        };

        await UpdateUserInfo(updateData);
        ElMessage.success('个人信息更新成功');

        // 通知父组件刷新用户信息
        refreshUserInfo();

    } catch (error) {
        console.error('更新个人信息失败:', error);
        const message = error.response?.data?.message || '更新失败';
        ElMessage.error(message);
    } finally {
        submitLoading.value = false;
    }
};

// 自定义上传方法
const customUpload = async (options) => {
    const { file } = options;

    try {
        uploadLoading.value = true;

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'avatar'); // 标识这是头像上传

        // 调用上传接口
        const response = await UploadFile(formData);
        console.log('头像上传成功:', response);

        // 根据实际接口返回格式处理
        if (response && (response.url || response.file_url || response.path)) {
            const avatarUrl = response.url || response.file_url || response.path;
            userForm.avatar = avatarUrl;
            ElMessage.success('头像上传成功');

            // 可选：立即更新用户信息到服务器
            await updateAvatarToServer(avatarUrl);
        } else {
            throw new Error('上传响应格式错误');
        }

    } catch (error) {
        console.error('头像上传失败:', error);
        const message = error.response?.data?.message || error.message || '头像上传失败';
        ElMessage.error(message);
    } finally {
        uploadLoading.value = false;
    }
};

// 更新头像到服务器
const updateAvatarToServer = async (avatarUrl) => {
    try {
        const updateData = {
            name: userForm.name,
            email: userForm.email,
            avatar: avatarUrl,
            data: {}
        };

        await UpdateUserInfo(updateData);
        console.log('头像信息已同步到服务器');

        // 通知父组件刷新用户信息
        refreshUserInfo();
    } catch (error) {
        console.error('同步头像信息失败:', error);
        // 这里不显示错误提示，因为上传已经成功了
    }
};

const beforeAvatarUpload = (rawFile) => {
    const isImage = rawFile.type.startsWith('image/');
    const isLt2M = rawFile.size / 1024 / 1024 < 2;

    if (!isImage) {
        ElMessage.error('只能上传图片文件!');
        return false;
    }
    if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!');
        return false;
    }
    return true;
};

onMounted(() => {
    loadUserInfo();
});
</script>
<style lang="scss" scoped>
.avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.user_box_main_form_btn {
    cursor: pointer;
    transition: opacity 0.2s;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.user_box_main_form_upload {
    transition: opacity 0.2s;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

@import url('./user.scss');
</style>
<style>
.avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    background: #ffffff;
}

.avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    text-align: center;
}
</style>
