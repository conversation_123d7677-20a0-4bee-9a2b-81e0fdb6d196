<template>
    <div class="center_box">
        <div class="center_box_tabs">
            <div class="center_box_tabs_item">
                <img src="@assets/waitPay.png" class="center_box_tabs_item_icon" />
                <div class="center_box_tabs_item_main">
                    <div class="center_box_tabs_item_main_num">{{ orderStats.waitPay || 0 }}</div>
                    <div class="center_box_tabs_item_main_title">{{ t('center.waitPay') }}</div>
                </div>
            </div>
            <div class="center_box_tabs_item">
                <img src="@assets/waitDevely.png" class="center_box_tabs_item_icon" />
                <div class="center_box_tabs_item_main">
                    <div class="center_box_tabs_item_main_num">{{ orderStats.waitDeliver || 0 }}</div>
                    <div class="center_box_tabs_item_main_title">{{ t('center.waitDeliver') }}</div>
                </div>
            </div>
            <div class="center_box_tabs_item">
                <img src="@assets/waitPay.png" class="center_box_tabs_item_icon" />
                <div class="center_box_tabs_item_main">
                    <div class="center_box_tabs_item_main_num">{{ orderStats.waitReceive || 0 }}</div>
                    <div class="center_box_tabs_item_main_title">{{ t('center.waitReceive') }}</div>
                </div>
            </div>
            <div class="center_box_tabs_item">
                <img src="@assets/waitDevely.png" class="center_box_tabs_item_icon" />
                <div class="center_box_tabs_item_main">
                    <div class="center_box_tabs_item_main_num">{{ orderStats.waitAfterSale || 0 }}</div>
                    <div class="center_box_tabs_item_main_title">{{ t('center.waitAfterSale') }}</div>
                </div>
            </div>
        </div>
        <div class="center_box_section">
            <div class="center_box_section_title">
                {{ t('center.title') }}
                <div class="center_box_section_title_all" @click="goToOrderList">
                    {{ t('center.all') }}
                    <img src="@assets/arrowIcon.png" class="center_box_section_title_all_icon" />
                </div>
            </div>
            <div class="center_box_section_line"></div>
            <div class="center_box_section_list" v-if="recentOrders.length > 0">
                <div class="center_box_section_list_item" v-for="order in recentOrders" :key="order.number">
                    <div class="order_item_info">
                        <div class="order_number">{{ t('center.orderNo') }}：{{ order.number }}</div>
                        <div class="order_date">{{ formatDate(order.created_at) }}</div>
                        <div class="order_status">{{ getOrderStatusText(order.status) }}</div>
                        <div class="order_total">{{ order.total_format }}</div>
                    </div>
                </div>
            </div>
            <div v-else class="empty_orders">
                {{ t('center.noOrders') }}
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { GetOrderList } from '@api/order';

const { t, locale } = useI18n();
const router = useRouter();

const orderStats = ref({
    waitPay: 0,
    waitDeliver: 0,
    waitReceive: 0,
    waitAfterSale: 0
});

const recentOrders = ref([]);
const loading = ref(false);

// 获取订单统计数据
const loadOrderStats = async () => {
    try {
        loading.value = true;

        // 获取各状态的订单数量
        const [waitPayRes, waitDeliverRes, waitReceiveRes, waitAfterSaleRes, recentRes] = await Promise.all([
            GetOrderList({ status: 'pending_payment', limit: 1 }),
            GetOrderList({ status: 'processing', limit: 1 }),
            GetOrderList({ status: 'shipped', limit: 1 }),
            GetOrderList({ status: 'refund_pending', limit: 1 }),
            GetOrderList({ limit: 1 }) // 获取最近5个订单
        ]);

        orderStats.value = {
            waitPay: waitPayRes.total || 0,
            waitDeliver: waitDeliverRes.total || 0,
            waitReceive: waitReceiveRes.total || 0,
            waitAfterSale: waitAfterSaleRes.total || 0
        };

        recentOrders.value = recentRes.orders || [];

    } catch (error) {
        console.error('获取订单统计失败:', error);
    } finally {
        loading.value = false;
    }
};

// 格式化日期
const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
    const statusMap = {
        'pending_payment': t('center.waitPay'),
        'processing': t('center.waitDeliver'),
        'shipped': t('center.waitReceive'),
        'completed': t('center.completed'),
        'cancelled': t('center.cancelled'),
        'refund_pending': t('center.waitAfterSale')
    };
    return statusMap[status] || status;
};

// 跳转到订单列表
const goToOrderList = () => {
    router.push('/order');
};

onMounted(() => {
    loadOrderStats();
});
</script>
<style lang="scss" scoped>
@import url('./center.scss');

.order_item_info {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;

    .order_number {
        font-size: 1.4rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .order_date {
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 0.5rem;
    }

    .order_status {
        font-size: 1.2rem;
        color: #6e4aeb;
        margin-bottom: 0.5rem;
    }

    .order_total {
        font-size: 1.4rem;
        font-weight: 600;
        color: #333;
    }
}

.empty_orders {
    text-align: center;
    padding: 2rem;
    color: #999;
    font-size: 1.4rem;
}

.center_box_section_title_all {
    cursor: pointer;
}
</style>