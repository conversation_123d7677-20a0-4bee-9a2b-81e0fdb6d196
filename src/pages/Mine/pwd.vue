<template>
    <div class="pwd_box">
        <div class="pwd_box_title">{{ t('center.pwd') }}</div>
        <div class="pwd_box_line"></div>
        <div class="pwd_box_form" v-loading="loading">
            <div class="pwd_box_form_control">
                <input v-model="passwordForm.current_password" class="pwd_box_form_control_val"
                    :placeholder="t('center.placeholder1')" type="password" />
            </div>
            <div class="pwd_box_form_control">
                <input v-model="passwordForm.new_password" class="pwd_box_form_control_val"
                    :placeholder="t('center.placeholder2')" type="password" />
            </div>
            <div class="pwd_box_form_control">
                <input v-model="passwordConfirm" class="pwd_box_form_control_val"
                    :placeholder="t('center.placeholder3')" type="password" />
            </div>
            <div class="pwd_box_form_btn" @click="onSubmit" :disabled="submitLoading">
                {{ submitLoading ? '提交中...' : t('center.submit') }}
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { UpdatePassword } from '@api/account';

const { t, locale } = useI18n();
const router = useRouter();

const loading = ref(false);
const submitLoading = ref(false);
const passwordConfirm = ref('');

const passwordForm = reactive({
    current_password: '',
    new_password: ''
});

// 表单验证
const validateForm = () => {
    if (!passwordForm.current_password) {
        ElMessage.error('请输入当前密码');
        return false;
    }

    if (!passwordForm.new_password) {
        ElMessage.error('请输入新密码');
        return false;
    }

    if (passwordForm.new_password.length < 6) {
        ElMessage.error('新密码长度不能少于6位');
        return false;
    }

    if (!passwordConfirm.value) {
        ElMessage.error('请确认新密码');
        return false;
    }

    if (passwordForm.new_password !== passwordConfirm.value) {
        ElMessage.error('两次输入的新密码不一致');
        return false;
    }

    if (passwordForm.current_password === passwordForm.new_password) {
        ElMessage.error('新密码不能与当前密码相同');
        return false;
    }

    return true;
};

// 提交修改密码
const onSubmit = async () => {
    if (!validateForm()) {
        return;
    }

    try {
        submitLoading.value = true;

        const data = {
            old_password: passwordForm.current_password,
            new_password: passwordForm.new_password,
            new_password_confirmation: passwordConfirm.value
        };

        await UpdatePassword(data);
        ElMessage.success('密码修改成功');

        // 清空表单
        passwordForm.current_password = '';
        passwordForm.new_password = '';
        passwordConfirm.value = '';

    } catch (error) {
        console.error('修改密码失败:', error);
        // const message = error.response?.data?.message || '密码修改失败';
        // ElMessage.error(message);
    } finally {
        submitLoading.value = false;
    }
};
</script>
<style lang="scss" scoped>
@import url('./pwd.scss');

.pwd_box_form_btn {
    cursor: pointer;
    transition: opacity 0.2s;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}
</style>