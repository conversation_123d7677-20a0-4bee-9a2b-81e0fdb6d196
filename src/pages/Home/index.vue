<template>
    <div class="home_view">
        <swiper :modules="modules" :pagination="{
            clickable: true,
        }" :navigation="true" :slidesPerView="1" :spaceBetween="30" :loop="true" class="home_view_swiper">
            <swiper-slide v-for="(item, index) in slideshowImages" :key="index">
                <img :src="item.showUrl" class="home_view_banner" />
            </swiper-slide>
        </swiper>
        <div class="home_view_product">
            <div class="home_view_product_title">
                {{ t('home.title') }}
                <div class="home_view_product_title_btn" @click="onShowList()">
                    {{ t('home.shopmall') }}
                    <img src="@assets/arrow.png" class="home_view_product_title_btn_icon" />
                </div>
            </div>
            <goodsList :products="productList" />
        </div>
        <div class="home_view_vedio">
            <video src="http://***************/catalog/录屏2025-06-21 10.09.22.mov" class="home_view_vedio_url"
                controls />
        </div>
        <div class="home_view_ledger">
            <img src="@assets/ledger.png" class="home_view_ledger_img" />
            <div class="home_view_ledger_main">
                <p>
                    {{ t('home.use') }}
                    <span class="puper">Ledger</span>
                    {{ t('home.wallet') }}
                </p>
                <p>{{ t('home.tip') }}</p>
            </div>
        </div>
        <div class="home_view_banner">
            <img src="@assets/banner.png" class="home_view_banner_img" />
        </div>
        <div class="home_view_introduce">
            <div class="home_view_introduce_title">
                <span>Ledger Live</span>{{ t('home.introduce') }}
            </div>
            <div class="home_view_introduce_desc">{{ t('home.desc') }}</div>
            <div class="home_view_introduce_list">
                <div class="home_view_introduce_list_item">
                    <img src="@assets/icon1.png" class="home_view_introduce_list_item_icon" />
                    <div class="home_view_introduce_list_item_name">{{ t('home.name1') }}</div>
                    <div class="home_view_introduce_list_item_desc">{{ t('home.desc1') }}</div>
                    <img src="@assets/img1.png" class="home_view_introduce_list_item_img" />
                </div>
                <div class="home_view_introduce_list_item">
                    <img src="@assets/icon2.png" class="home_view_introduce_list_item_icon" />
                    <div class="home_view_introduce_list_item_name">{{ t('home.name2') }}</div>
                    <div class="home_view_introduce_list_item_desc">{{ t('home.desc2') }}</div>
                    <img src="@assets/img2.png" class="home_view_introduce_list_item_img" />
                </div>
                <div class="home_view_introduce_list_item">
                    <img src="@assets/icon3.png" class="home_view_introduce_list_item_icon" />
                    <div class="home_view_introduce_list_item_name">{{ t('home.name3') }}</div>
                    <div class="home_view_introduce_list_item_desc">{{ t('home.desc3') }}</div>
                    <img src="@assets/img3.png" class="home_view_introduce_list_item_img" />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { GetHomeInfo, GetLatestProducts } from '@api';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import goodsList from '@components/goodsList.vue';
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();
const router = useRouter();
const modules = ref([Navigation, Pagination]);

const detail = ref({});
const homeData = ref({});
const slideshowImages = ref([]);
const productList = ref([]); // 新增：用于存储商品列表

// 根据当前语言获取图片链接
const getImageByLanguage = (imageObj) => {
    const currentLocale = locale.value;
    // 语言映射，将 vue-i18n 的语言代码映射到 API 返回的语言字段
    const languageMap = {
        'zh': 'zh_cn',
        'zh-CN': 'zh_cn',
        'en': 'en',
        'es': 'es',
        'fr': 'fr',
        'de': 'de',
        'it': 'it',
        'ja': 'ja',
        'ko': 'ko',
        'id': 'id',
        'ru': 'ru'
    };

    const apiLanguage = languageMap[currentLocale] || 'en';
    return imageObj[apiLanguage] || imageObj['en'] || '';
};

// 处理轮播图数据
const processSlideshow = (modules) => {
    if (!modules || !Array.isArray(modules)) return;
    const slideshowModule = modules.filter(module => module.code === 'slideshow');
    if (!slideshowModule.length) return;
    const images = slideshowModule.map(item => item.content.images[0])
    slideshowImages.value = images.map(img => ({
        ...img,
        showUrl: `http://************${getImageByLanguage(img.image)}`
    }));

};

const change = () => {
    locale.value = 'zh'
}

const onShowDetail = (id) => {
    router.push(`/goods/${id}`)
}

const onShowList = () => {
    router.push(`/goods`)
}

const onSwiper = (swiper) => {
    console.log(swiper);
};

const onSlideChange = () => {
    console.log('slide change');
};

onMounted(() => {
    onLoadData();
    GetLatestProducts().then(res => {
        console.log('最新商品', res);
        productList.value = res.slice(0, 4); // 获取前4个商品
    })


});

const onLoadData = () => {
    // 获取首页数据
    GetHomeInfo()
        .then(res => {
            console.log('首页数据', res);
            if (res) {
                homeData.value = res;
                // 处理轮播图数据
                processSlideshow(res.modules);

                // 保持原有的逻辑
                if (res.modules) {
                    res.modules.forEach(module => {
                        detail.value[module.code] = module;
                    });
                }
            }
        })
        .catch(error => {
            console.error('获取首页数据失败:', error);
        });

    // 获取最新商品数据
    GetLatestProducts()
        .then(res => {
            console.log('最新商品数据', res);
            if (res && res.products && res.products.data) {
                // 只显示前4个商品
                productList.value = res.products.data.slice(0, 4);
            }
        })
        .catch(error => {
            console.error('获取商品数据失败:', error);
        });
}
</script>
<style lang="scss" scoped>
@import url('./index.scss');
</style>