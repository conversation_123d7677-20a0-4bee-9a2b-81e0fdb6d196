<template>
    <div class="login_view">
        <p class="login_view_title">首页 / 用户登录与注册</p>
        <div class="login_view_main">
            <div class="login_view_main_form">
                <div class="login_view_main_form_title">{{ t('login.login') }}</div>
                <div class="login_view_main_form_box mb12">
                    <div class="login_view_main_form_box_input">
                        <input class="login_view_main_form_box_input_val" :placeholder="t('login.placeholder')"
                            v-model="loginForm.email" type="text" />
                    </div>
                    <div class="login_view_main_form_box_input">
                        <input class="login_view_main_form_box_input_val" :placeholder="t('login.password')"
                            v-model="loginForm.password" type="password" />
                    </div>
                </div>
                <div class="login_view_main_form_tip">
                    <img src="@assets/help.png" class="login_view_main_form_tip_icon" />
                    {{ t('login.forget') }}
                </div>
                <div class="login_view_main_form_btn" @click="doLogin()">{{ t('login.login') }}</div>
            </div>
            <div class="login_view_main_form">
                <div class="login_view_main_form_title">{{ t('login.register') }}</div>
                <div class="login_view_main_form_box">
                    <div class="login_view_main_form_box_input">
                        <input class="login_view_main_form_box_input_val" :placeholder="t('login.placeholder')"
                            v-model="registerForm.email" type="text" />
                    </div>
                    <div class="login_view_main_form_box_input">
                        <input class="login_view_main_form_box_input_val" :placeholder="t('login.password')"
                            v-model="registerForm.password" type="password" />
                    </div>
                </div>
                <div class="login_view_main_form_btn" @click="doRegister()">{{ t('login.register') }}</div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { Login, Register } from '@api/auth';
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';
const { t, locale } = useI18n();
const router = useRouter();
const userStore = useUserStore();

const registerForm = reactive({
    email: '',
    password: ''
});

const loginForm = reactive({
    email: '',
    password: ''
});

const doLogin = () => {
    if (!loginForm.email || !loginForm.password) {
        ElMessage({
            message: '请输入邮箱和密码',
            type: 'warning'
        });
        return;
    }
    let emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
    if (!emailReg.test(loginForm.email)) {
        ElMessage({
            message: '请输入正确的邮箱',
            type: 'warning'
        });
        return;
    }
    Login({
        email: loginForm.email,
        password: loginForm.password
    }).then(res => {
        console.log('登录响应:', res);

        // 存储登录信息到store和localStorage
        userStore.login(res);

        ElMessage({
            message: '登录成功',
            type: 'success'
        });

        // 登录成功后跳转（优先跳转到重定向页面，否则跳转到首页）
        const redirect = router.currentRoute.value.query.redirect || '/';
        router.replace(redirect);
    }).catch(error => {
        console.error('登录失败:', error);
        ElMessage({
            message: '登录失败，请检查邮箱和密码',
            type: 'error'
        });
    })
}

const doRegister = () => {
    if (!registerForm.email || !registerForm.password) {
        ElMessage({
            message: '请输入邮箱和密码',
            type: 'warning'
        });
        return;
    }
    let emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
    if (!emailReg.test(registerForm.email)) {
        ElMessage({
            message: '请输入正确的邮箱',
            type: 'warning'
        });
        return;
    }
    Register({
        email: registerForm.email,
        password: registerForm.password,
        password_confirmation: registerForm.password
    }).then(res => {
        console.log('注册响应:', res);

        // 注册成功后也可以直接登录
        userStore.login(res);

        ElMessage({
            message: '注册成功',
            type: 'success'
        });

        // 跳转到首页
        router.replace('/');
    }).catch(error => {
        console.error('注册失败:', error);
        ElMessage({
            message: '注册失败，请稍后重试',
            type: 'error'
        });
    })
}
</script>
<style lang="scss" scoped>
@import url('./index.scss');
</style>