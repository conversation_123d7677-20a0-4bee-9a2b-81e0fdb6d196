import { get, post } from '@utils/request';

/**
 * PayPal支付
 * @param {string} orderNumber - 订单号
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const PayWithPayPal = (orderNumber, paymentData = {}) => {
  return post(`/v1/orders/${orderNumber}/pay/paypal`, paymentData);
};

/**
 * Stripe支付
 * @param {string} orderNumber - 订单号
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const PayWithStripe = (orderNumber, paymentData = {}) => {
  return post(`/v1/orders/${orderNumber}/pay/stripe`, paymentData);
};

/**
 * BitPay支付
 * @param {string} orderNumber - 订单号
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const PayWithBitPay = (orderNumber, paymentData = {}) => {
  return post(`/v1/orders/${orderNumber}/pay/bitpay`, paymentData);
};

/**
 * 微信支付
 * @param {string} orderNumber - 订单号
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const PayWithWePay = (orderNumber, paymentData = {}) => {
  return post(`/v1/orders/${orderNumber}/pay/wepay`, paymentData);
};

/**
 * 获取支付状态
 * @param {string} orderNumber - 订单号
 * @param {string} paymentMethod - 支付方式
 * @returns {Promise}
 */
export const GetPaymentStatus = (orderNumber, paymentMethod) => {
  return get(`/v1/orders/${orderNumber}/pay/status`, {
    params: { method: paymentMethod }
  });
};

/**
 * 验证支付结果
 * @param {string} orderNumber - 订单号
 * @param {string} paymentMethod - 支付方式
 * @param {Object} verificationData - 验证数据
 * @returns {Promise}
 */
export const VerifyPayment = (orderNumber, paymentMethod, verificationData) => {
  return post(`/v1/orders/${orderNumber}/pay/verify`, {
    method: paymentMethod,
    ...verificationData
  });
};
