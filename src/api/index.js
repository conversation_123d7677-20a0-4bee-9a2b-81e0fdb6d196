// 统一导出所有 API 接口
export * as authAPI from './auth';
export * as homeAPI from './home';
export * as productAPI from './product';
export * as cartAPI from './cart';
export * as checkoutAPI from './checkout';
export * as orderAPI from './order';
export * as accountAPI from './account';
export * as addressAPI from './address';
export * as wishlistAPI from './wishlist';

// 也可以单独导入使用
export { Login, Register, Logout } from './auth';
export { GetHomeInfo, GetLatestProducts } from './home';
export { GetProductList, GetProductDetail } from './product';
export {
  GetCartList,
  GetMiniCart,
  AddToCart,
  UpdateCart,
  SelectCartItems,
  UnselectCartItems,
  RemoveCartItem,
} from './cart';
export {
  GetCheckoutInfo,
  UpdateCheckout,
  ConfirmOrder,
  GetCheckoutSuccess,
} from './checkout';
export {
  GetOrderDetail,
  GetOrderPayInfo,
  CancelOrder,
  CompleteOrder,
} from './order';
export {
  GetAccountInfo,
  GetUserInfo,
  UpdateUserInfo,
  GetPasswordPage,
  UpdatePassword,
  GetUserOrders,
  GetUserOrderDetail,
  GetRMAList,
  GetRMADetail,
  CreateRMAPage,
  SubmitRMA,
} from './account';
export {
  GetAddressList,
  GetAddressDetail,
  CreateAddress,
  UpdateAddress,
  DeleteAddress,
  GetZones,
} from './address';
export { GetWishlist, AddToWishlist, RemoveFromWishlist } from './wishlist';
