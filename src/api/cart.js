import { get, post, put, del } from '@utils/request';

/**
 * 获取购物车列表
 * @returns {Promise}
 */
export const GetCartList = () => {
  return get('/v1/carts');
};

/**
 * 获取迷你购物车信息
 * @returns {Promise}
 */
export const GetMiniCart = () => {
  return get('/v1/carts/mini');
};

/**
 * 添加商品到购物车
 * @param {Object} data - 商品信息
 * @param {boolean} data.buy_now - 是否现在购买
 * @param {number} data.quantity - 数量
 * @param {number} data.sku_id - SKU ID
 * @returns {Promise}
 */
export const AddToCart = (data) => {
  return post('/v1/carts', data);
};

/**
 * 更新购物车商品
 * @param {number} cartId - 购物车ID
 * @param {Object} data - 更新数据
 * @param {number} data.quantity - 数量
 * @param {number} data.sku_id - SKU ID
 * @returns {Promise}
 */
export const UpdateCart = (cartId, data) => {
  return put(`/v1/carts/${cartId}`, data);
};

/**
 * 选中购物车商品
 * @param {Array} cartIds - 购物车商品ID数组
 * @returns {Promise}
 */
export const SelectCartItems = (cartIds) => {
  return post('/v1/carts/select', { cart_ids: cartIds });
};

/**
 * 取消选中购物车商品
 * @param {Array} cartIds - 购物车商品ID数组
 * @returns {Promise}
 */
export const UnselectCartItems = (cartIds) => {
  return post('/v1/carts/unselect', { cart_ids: cartIds });
};

/**
 * 删除购物车商品
 * @param {number} cartId - 购物车ID
 * @returns {Promise}
 */
export const RemoveCartItem = (cartId) => {
  return del(`/v1/carts/${cartId}`);
};
