import { get, post } from '@utils/request';

/**
 * 用户登录
 * @param {string} email - 邮箱
 * @param {string} password - 密码
 * @returns {Promise}
 */
export const Login = (data) => {
  return post('/v1/login', data);
};

/**
 * 用户注册
 * @param {string} email - 邮箱
 * @param {string} password - 密码
 * @returns {Promise}
 */
export const Register = (data) => {
  return post('/v1/register', data);
};

/**
 * 用户登出
 * @returns {Promise}
 */
export const Logout = () => {
  return get('/v1/logout');
};
